﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using Zendar.Data.Enums;

namespace Zendar.Data.ViewModels
{
    public class RelatorioEstoqueFiltrosViewModel
    {
        public CampoPersonalizadoValorViewModel[] CamposPersonalizados { get; set; }
        public TipoRelatorioEstoque TipoRelatorio { get; set; }
        public Guid? ProdutoId { get; set; }
        [Description("Tipo estoque")]
        public TipoFiltroProdutoEstoque TipoEstoque { get; set; }
        [Description("Status")]
        public StatusConsulta StatusConsulta { get; set; }
        public List<Guid> Tamanhos { get; set; }
        public List<Guid> Cores { get; set; }
        public List<Guid> CategoriasProduto { get; set; }
        public List<Guid> Marcas { get; set; }
        public List<Guid> Tags { get; set; }
        public List<Guid> LocalEstoqueIds { get; set; } = new();
        public Guid? TabelaPrecoId { get; set; }
    }
}
