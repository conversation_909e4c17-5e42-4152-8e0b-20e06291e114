import api, { ResponseApi } from 'services/api';

export type GerarRelatorioEstoqueParametros = {
  cores?: string[];
  categoriasProduto?: string[];
  localEstoqueIds?: string[];
  tags?: string[];
  marcas?: string[];
  produtoId?: string;
  tamanhos?: string[];
  tipoEstoque: number;
  tipoRelatorio: number;
  tabelaPrecoId?: string;
  camposPersonalizados:
    | {
        campoPersonalizadoId: string;
        valor: string;
      }[]
    | null;
};

export type GerarRelatorioEstoqueEntrada = {
  endpoint: string;
  dados: GerarRelatorioEstoqueParametros;
};

export const gerarRelatorioEstoque = ({
  endpoint,
  dados,
}: GerarRelatorioEstoqueEntrada) => {
  return api.post<void, ResponseApi<string>>(endpoint, { ...dados });
};
