using Multiempresa.Shared.Helpers.Formatadores;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Zendar.Business.Helpers.ImpressaoRelatorioPdf;
using Zendar.Business.Interfaces;
using Zendar.Business.Interfaces.Services;
using Zendar.Business.Services.V2.TabelaPrecoV2Services.CalcularPrecoProdutoService;
using Zendar.Business.ViewModels;
using Zendar.Business.ViewModels.Relatorios;
using Zendar.Data.Enums;
using Zendar.Data.Helpers;
using Zendar.Data.Interfaces.Aplicacao;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.Resources.Mensagens;
using Zendar.Data.ViewModels;

namespace Zendar.Business.Services.EstoqueServices
{
    public class RelatorioEstoqueService : BaseService, IRelatorioEstoqueService
    {
        private readonly ILojaRepository _lojaRepository;
        private readonly IAspNetUserInfo _aspNetUserInfo;
        private readonly ILogAuditoriaService _logAuditoriaService;
        private readonly IProdutoCorTamanhoRepository _produtoCorTamanhoRepository;
        private readonly IPadronizacaoService _padronizacaoService;
        private readonly IProdutoCorTamanhoService _produtoCorTamanhoService;
        private readonly ITamanhoRepository _tamanhoRepository;
        private readonly ICorRepository _corRepository;
        private readonly IMarcaRepository _marcaRepository;
        private readonly ITagRepository _tagRepository;
        private readonly ITabelaPrecoRepository _tabelaPrecoRepository;
        private readonly ICategoriaProdutoRepository _categoriaProdutoRepository;
        private readonly ICampoPersonalizadoRepository _campoPersonalizadoRepository;
        private readonly ICategoriaProdutoService _categoriaProdutoService;
        private readonly ILocalEstoqueRepository _localEstoqueRepository;
        private readonly ICalculoPrecoProdutoV2Service _calculoPrecoProdutoV2Service;

        public RelatorioEstoqueService(INotificador notificador,
                                       ILojaRepository lojaRepository,
                                       ILogAuditoriaService logAuditoriaService,
                                       IAspNetUserInfo aspNetUserInfo,
                                       IPadronizacaoService padronizacaoService,
                                       IProdutoCorTamanhoRepository produtoCorTamanhoRepository,
                                       IProdutoCorTamanhoService produtoCorTamanhoService,
                                       ITamanhoRepository tamanhoRepository,
                                       ICorRepository corRepository,
                                       IMarcaRepository marcaRepository,
                                       ITagRepository tagRepository,
                                       ICategoriaProdutoRepository categoriaProdutoRepository,
                                       ICampoPersonalizadoRepository campoPersonalizadoRepository,
                                       ICategoriaProdutoService categoriaProdutoService,
                                       ILocalEstoqueRepository localEstoqueRepository,
                                       ITabelaPrecoRepository tabelaPrecoRepository,
                                       ICalculoPrecoProdutoV2Service calculoPrecoProdutoV2Service) : base(notificador)
        {
            _lojaRepository = lojaRepository;
            _logAuditoriaService = logAuditoriaService;
            _aspNetUserInfo = aspNetUserInfo;
            _padronizacaoService = padronizacaoService;
            _produtoCorTamanhoService = produtoCorTamanhoService;
            _tamanhoRepository = tamanhoRepository;
            _corRepository = corRepository;
            _marcaRepository = marcaRepository;
            _tagRepository = tagRepository;
            _categoriaProdutoRepository = categoriaProdutoRepository;
            _campoPersonalizadoRepository = campoPersonalizadoRepository;
            _categoriaProdutoService = categoriaProdutoService;
            _produtoCorTamanhoRepository = produtoCorTamanhoRepository;
            _localEstoqueRepository = localEstoqueRepository;
            _tabelaPrecoRepository = tabelaPrecoRepository;
            _calculoPrecoProdutoV2Service = calculoPrecoProdutoV2Service;
        }

        private List<Guid> TratarFiltroCategoria(List<Guid> categoriaId)
        {
            var listaIdCategorias = new List<Guid>();

            foreach (var filtro in categoriaId)
            {
                listaIdCategorias.AddRange(_categoriaProdutoService.ObterCategoriasVinculadas(filtro).Result);
            }

            categoriaId.AddRange(listaIdCategorias);

            return categoriaId;
        }

        public async Task<byte[]> GerarRelatorio(RelatorioEstoqueFiltrosViewModel filtrosEstoqueViewModel, bool csv)
        {
            if (filtrosEstoqueViewModel.CategoriasProduto != null)
                filtrosEstoqueViewModel.CategoriasProduto = TratarFiltroCategoria(filtrosEstoqueViewModel.CategoriasProduto);

            var loja = await _lojaRepository.FirstOrDefaultAsNoTracking(x => x.Id == _aspNetUserInfo.LojaId.Value, x => new Loja
            {
                Fantasia = x.Fantasia
            });

            var produtos = await _produtoCorTamanhoRepository.ObterRelatoriosEstoque(new RelatorioProdutoFiltrosViewModel
            {
                CamposPersonalizados = filtrosEstoqueViewModel.CamposPersonalizados,
                ProdutoId = filtrosEstoqueViewModel.ProdutoId,
                TipoEstoque = filtrosEstoqueViewModel.TipoEstoque,
                StatusConsulta = filtrosEstoqueViewModel.StatusConsulta,
                Tamanhos = filtrosEstoqueViewModel.Tamanhos,
                Cores = filtrosEstoqueViewModel.Cores,
                CategoriasProduto = filtrosEstoqueViewModel.CategoriasProduto,
                Marcas = filtrosEstoqueViewModel.Marcas,
                Tags = filtrosEstoqueViewModel.Tags,
                LocalEstoqueIds = filtrosEstoqueViewModel.LocalEstoqueIds
            }, _aspNetUserInfo.LojaId.Value);

            if (produtos == null || produtos.Count == 0)
            {
                NotificarAviso(ResourceMensagem.NenhumRegistroEncontrado);
                return null;
            }

            byte[] relatorio;

            if (csv)
            {
                relatorio = Encoding.ASCII.GetBytes(await GerarCSV(produtos, filtrosEstoqueViewModel.TipoRelatorio, filtrosEstoqueViewModel.TabelaPrecoId));
            }
            else
            {
                relatorio = await GerarImpressao(produtos, new RelatorioEstoqueViewModel
                {
                    TipoSistema = _aspNetUserInfo.Sistema,
                    Emissao = DateTime.UtcNow.AddHours(_aspNetUserInfo.TimezoneOffset.Value),
                    LojaFantasia = loja.Fantasia,
                    TipoRelatorio = filtrosEstoqueViewModel.TipoRelatorio,
                    Filtros = await GerarTextoExibicaoFiltros(filtrosEstoqueViewModel),
                    TotalItens = produtos.Count.ToString()
                }, filtrosEstoqueViewModel.TabelaPrecoId);
            }

            await _logAuditoriaService.Inserir(new LogAuditoriaInserirViewModel(LogAuditoriaTela.IMPRESSAO_PERSONALIZADA, LogAuditoriaOperacao.GERAR,
                $"Relatorio: {filtrosEstoqueViewModel.TipoRelatorio.ObterDescricao()}"));

            return relatorio;
        }

        private async Task<byte[]> GerarImpressao(List<ProdutoCorTamanhoPrecoViewModel> produtos, RelatorioEstoqueViewModel estoqueVm, Guid? tabelaPrecoId = null)
        {
            decimal saldoTotal = 0;
            decimal totalPreco = 0;

            string referencia;
            decimal saldoAtual = 0;
            decimal preco = 0;

            var casasDecimais = await _padronizacaoService.ObterCasasDecimais();
            estoqueVm.Estoques = new List<EstoqueViewModel>();

            // Usar a tabela de preço fornecida ou a padrão do sistema
            var tabelaPreco = tabelaPrecoId.HasValue
                ? await _tabelaPrecoRepository.FirstAsNoTracking(t => t.Id == tabelaPrecoId.Value)
                : await _tabelaPrecoRepository.FirstAsNoTracking(t => t.PadraoSistema);

            // Se a tabela de preço fornecida não for encontrada, usar a padrão do sistema
            if (tabelaPreco == null && tabelaPrecoId.HasValue)
            {
                tabelaPreco = await _tabelaPrecoRepository.FirstAsNoTracking(t => t.PadraoSistema);
            }

            foreach (var p in produtos)
            {
                preco = estoqueVm.TipoRelatorio == TipoRelatorioEstoque.RELATORIO_PRECO_VENDA
                      ? await _calculoPrecoProdutoV2Service.CalcularPreco(tabelaPreco.Id, p.Id, _aspNetUserInfo.LojaId.Value)
                      : p.PrecoCusto;

                referencia = p?.Referencia;
                saldoAtual = p.SaldoAtual;

                estoqueVm.Estoques.Add(new EstoqueViewModel
                {
                    Produto = _produtoCorTamanhoService.FormatarDescricaoCompletaProduto(p.Id).Result,
                    Referencia = referencia,
                    Saldo = FormatarValor.FormatarValorComPontuacao(saldoAtual, casasDecimais.CasasDecimaisQuantidade),
                    Preco = FormatarValor.FormatarValorComPontuacao(preco, casasDecimais.CasasDecimaisValor),
                    PrecoTotal = FormatarValor.FormatarValorComPontuacao(preco * saldoAtual)

                });

                saldoTotal += saldoAtual;
                totalPreco += preco * saldoAtual;
            }

            estoqueVm.TotalSaldo = FormatarValor.FormatarValorComPontuacao(saldoTotal, casasDecimais.CasasDecimaisQuantidade);
            estoqueVm.TotalPreco = FormatarValor.FormatarValorComPontuacao(totalPreco);

            return new ImpressaoRelatorioEstoque(estoqueVm).ToArray();
        }

        private async Task<string> GerarTextoExibicaoFiltros(RelatorioEstoqueFiltrosViewModel filtrosEstoqueViewModel)
        {
            var filtro = new StringBuilder();

            if (filtrosEstoqueViewModel.TipoEstoque != TipoFiltroProdutoEstoque.TODOS)
            {
                if (filtro.Length > 0) filtro.Append(" | ");
                filtro.Append($"Estoque: {filtrosEstoqueViewModel.TipoEstoque.ObterDescricao()}");
            }

            if (filtrosEstoqueViewModel.StatusConsulta != StatusConsulta.Todos)
            {
                if (filtro.Length > 0) filtro.Append(" | ");
                filtro.Append($"Status: {filtrosEstoqueViewModel.StatusConsulta.ObterDescricao()}");
            }

            if (filtrosEstoqueViewModel.Tamanhos != null && filtrosEstoqueViewModel.Tamanhos.Count > 0)
            {
                if (filtro.Length > 0) filtro.Append(" | ");
                var tamanhos = await _tamanhoRepository.ObterDescricao(filtrosEstoqueViewModel.Tamanhos);
                if (tamanhos.Any())
                {
                    filtro.Append($"Tamanho: {string.Join(", ", tamanhos)}");
                }
            }

            if (filtrosEstoqueViewModel.Cores != null && filtrosEstoqueViewModel.Cores.Count > 0)
            {
                if (filtro.Length > 0) filtro.Append(" | ");
                var cores = await _corRepository.ObterDescricao(filtrosEstoqueViewModel.Cores);
                if (cores.Any())
                {
                    filtro.Append($"Cor: {string.Join(", ", cores)}");
                }
            }

            if (filtrosEstoqueViewModel.CategoriasProduto != null && filtrosEstoqueViewModel.CategoriasProduto.Count > 0)
            {
                if (filtro.Length > 0) filtro.Append(" | ");
                var categorias = await _categoriaProdutoRepository.ObterDescricao(filtrosEstoqueViewModel.CategoriasProduto);
                if (categorias.Any())
                {
                    filtro.Append($"Categoria: {string.Join(", ", categorias)}");
                }
            }

            if (filtrosEstoqueViewModel.Marcas != null && filtrosEstoqueViewModel.Marcas.Count > 0)
            {
                if (filtro.Length > 0) filtro.Append(" | ");
                var marcas = await _marcaRepository.ObterDescricao(filtrosEstoqueViewModel.Marcas);
                if (marcas.Any())
                {
                    filtro.Append($"Marca: {string.Join(", ", marcas)}");
                }
            }

            if (filtrosEstoqueViewModel.Tags != null && filtrosEstoqueViewModel.Tags.Count > 0)
            {
                if (filtro.Length > 0) filtro.Append(" | ");
                var tags = await _tagRepository.ObterDescricao(filtrosEstoqueViewModel.Tags);
                if (tags.Any())
                {
                    filtro.Append($"Tag: {string.Join(", ", tags)}");
                }
            }

            if (filtrosEstoqueViewModel.LocalEstoqueIds.Any())
            {
                if (filtro.Length > 0) filtro.Append(" | ");
                filtro.Append($"Local Estoque: ");

                var locaisEstoque = await _localEstoqueRepository.ObterNomes(filtrosEstoqueViewModel.LocalEstoqueIds);
                filtro.Append(string.Join(", ", locaisEstoque));
            }

            if (filtrosEstoqueViewModel.CamposPersonalizados != null && filtrosEstoqueViewModel.CamposPersonalizados.Length > 0)
            {
                if (filtro.Length > 0) filtro.Append(" | ");
                var campoPersonalizado = await _campoPersonalizadoRepository.ObterDescricao(filtrosEstoqueViewModel.CamposPersonalizados.Select(c => c.CampoPersonalizadoId).ToList());
                if (campoPersonalizado.Any())
                {
                    filtro.Append($"Campo personalizado: {string.Join(", ", campoPersonalizado)}");
                }
            }

            return filtro.ToString();
        }

        public async Task<string> GerarCSV(List<ProdutoCorTamanhoPrecoViewModel> produtos, TipoRelatorioEstoque tipoRelatorio, Guid? tabelaPrecoId = null)
        {
            string csv;

            if (tipoRelatorio == TipoRelatorioEstoque.RELATORIO_ESTOQUE)
            {
                var estoqueCSV = new List<EstoqueCSVViewModel>();

                foreach (var p in produtos)
                {
                    estoqueCSV.Add(new EstoqueCSVViewModel
                    {
                        Produto = _produtoCorTamanhoService.FormatarDescricaoCompletaProduto(p.Id).Result,
                        Referencia = p.Referencia,
                        Saldo = p.SaldoAtual
                    });
                }

                csv = EstoqueCSVViewModel.GerarCSVEstoque(estoqueCSV.OrderBy(x => x.Produto).ToList());
            }
            else
            {
                var estoquePrecoCSV = new List<EstoquePrecoCSVViewModel>();

                // Usar a tabela de preço fornecida ou a padrão do sistema
                var tabelaPreco = tabelaPrecoId.HasValue
                    ? await _tabelaPrecoRepository.FirstAsNoTracking(t => t.Id == tabelaPrecoId.Value)
                    : await _tabelaPrecoRepository.FirstAsNoTracking(t => t.PadraoSistema);

                // Se a tabela de preço fornecida não for encontrada, usar a padrão do sistema
                if (tabelaPreco == null && tabelaPrecoId.HasValue)
                {
                    tabelaPreco = await _tabelaPrecoRepository.FirstAsNoTracking(t => t.PadraoSistema);
                }

                foreach (var p in produtos)
                {
                    estoquePrecoCSV.Add(new EstoquePrecoCSVViewModel
                    {
                        Produto = _produtoCorTamanhoService.FormatarDescricaoCompletaProduto(p.Id).Result,
                        Referencia = p.Referencia,
                        Saldo = p.SaldoAtual,
                        PrecoVenda = await _calculoPrecoProdutoV2Service.CalcularPreco(tabelaPreco.Id, p.Id, _aspNetUserInfo.LojaId.Value),
                        PrecoCusto = p.PrecoCusto,
                        TipoRelatorio = tipoRelatorio
                    });
                }

                csv = EstoquePrecoCSVViewModel.GerarCSVEstoque(estoquePrecoCSV.OrderBy(x => x.Produto).ToList());
            }

            return csv;
        }

        public void Dispose()
        {
            _lojaRepository.Dispose();
            _produtoCorTamanhoRepository.Dispose();
            _padronizacaoService.Dispose();
            _produtoCorTamanhoService.Dispose();
            _tamanhoRepository.Dispose();
            _corRepository.Dispose();
            _marcaRepository.Dispose();
            _tagRepository.Dispose();
            _categoriaProdutoRepository.Dispose();
            _campoPersonalizadoRepository.Dispose();
            _categoriaProdutoService.Dispose();
            _logAuditoriaService.Dispose();
        }
    }
}


